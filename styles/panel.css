#panel {
  background-color: alpha(#010101, 0.1);
  border-bottom: 1px solid alpha(#010101, 0.025);
  transition: all 120ms ease-in-out;
}

#panel-icon {
  font-size: 18px;
}

#panel-button {
  border-radius: 8px;
  margin: 0 4px 0 4px;
  min-width: 20px;
  min-height: 20px;
}

#panel-button:hover {
  background-color: var(--surface-bright);
}

#battery-button {
  padding-right: 3px;
  padding-left: 3px;
  border-radius: 8px;
}

#battery-button:hover {
  background-color: var(--surface-bright);
}

#network-button {
  padding-right: 3px;
  padding-left: 3px;
  border-radius: 8px;
}

#network-button:hover {
  background-color: var(--surface-bright);
}

#bt-button {
  padding-right: 3px;
  padding-left: 3px;
  border-radius: 8px;
}

#bt-button:hover {
  background-color: var(--surface-bright);
}

#tray-button {
  border-radius: 8px;
  margin: 0 4px 0 0px;
  min-width: 20px;
  min-height: 20px;
}

#tray-button:hover {
  background-color: var(--surface-bright);
}

#date-time {
  margin: 0 10px;
}

#modules-left,
#modules-right {
  margin: 0 5px;
  padding: 3px 0;
}

.button {
  border-radius: 5px;
  padding: 0 5px;
  margin: 0 2.5px;
  background: radial-gradient(alpha(#aaa, 0) 0%, transparent, transparent);
  transition: all 100ms ease-in-out;
}

.button:hover {
  background: radial-gradient(alpha(#aaa, 0.2) 100%, transparent, transparent);
}


#modus-button label {
  font-size: 17px;
  margin: 0 10px;
}
